<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cat Profiles - Yendor Cats</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/profiles.css">
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo_shield.webp" alt="Yendor Cats Logo" class="logo-image">
                <span class="logo-text">Yendor Cats</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="index.html#gallery" class="nav-link">Gallery</a>
                </li>
                <li class="nav-item">
                    <a href="upload.html" class="nav-link">Upload</a>
                </li>
                <li class="nav-item">
                    <a href="profiles.html" class="nav-link active">Cat Profiles</a>
                </li>
                <li class="nav-item">
                    <a href="index.html#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <!-- Profiles Header -->
        <section class="profiles-header">
            <div class="container">
                <h1>Cat Profiles</h1>
                <p class="profiles-description">
                    Discover all our cats and their photo collections. Each profile shows the cat's timeline and photo history.
                </p>
                
                <!-- Search and Filter -->
                <div class="profiles-controls">
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Search cats by name..." class="search-input">
                        <button id="search-btn" class="search-btn">🔍</button>
                    </div>
                    
                    <div class="filter-container">
                        <select id="filter-breed" class="filter-select">
                            <option value="">All Breeds</option>
                        </select>
                        
                        <select id="filter-gender" class="filter-select">
                            <option value="">All Genders</option>
                            <option value="M">Male</option>
                            <option value="F">Female</option>
                        </select>
                        
                        <select id="sort-profiles" class="filter-select">
                            <option value="name-asc">Name A-Z</option>
                            <option value="name-desc">Name Z-A</option>
                            <option value="photos-desc">Most Photos</option>
                            <option value="photos-asc">Least Photos</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- Loading State -->
        <div class="loading-container" id="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading cat profiles...</p>
        </div>

        <!-- Profiles Grid -->
        <section class="profiles-section" id="profiles-section" style="display: none;">
            <div class="container">
                <div class="profiles-stats" id="profiles-stats">
                    <!-- Stats will be populated by JavaScript -->
                </div>
                
                <div class="profiles-grid" id="profiles-grid">
                    <!-- Profiles will be populated by JavaScript -->
                </div>
                
                <!-- Empty State -->
                <div class="empty-state" id="empty-state" style="display: none;">
                    <div class="empty-content">
                        <span class="empty-icon">🐱</span>
                        <h3>No Profiles Found</h3>
                        <p>No cat profiles match your current search criteria.</p>
                        <button id="clear-filters-btn" class="btn btn-primary">Clear Filters</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <div class="container">
                <div class="actions-grid">
                    <div class="action-card">
                        <div class="action-icon">📷</div>
                        <h3>Upload Photos</h3>
                        <p>Add new photos to existing cat profiles or create new ones</p>
                        <a href="upload.html" class="btn btn-primary">Upload Now</a>
                    </div>
                    
                    <div class="action-card">
                        <div class="action-icon">🏠</div>
                        <h3>View Gallery</h3>
                        <p>Browse all photos organized by categories</p>
                        <a href="index.html#gallery" class="btn btn-secondary">View Gallery</a>
                    </div>
                    
                    <div class="action-card">
                        <div class="action-icon">📞</div>
                        <h3>Contact Us</h3>
                        <p>Get in touch for more information about our cats</p>
                        <a href="index.html#contact" class="btn btn-secondary">Contact</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Yendor Cats</h3>
                    <p>Breeding exceptional Maine Coon cats with love and care.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#gallery">Gallery</a></li>
                        <li><a href="upload.html">Upload</a></li>
                        <li><a href="profiles.html">Cat Profiles</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: 0417281675</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Yendor Cats. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/navbar.js"></script>
    <script src="js/profiles.js"></script>
    <script src="js/live-reload.js"></script>
</body>
</html>
