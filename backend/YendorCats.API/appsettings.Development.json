{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=password;Port=3306;"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Vault": {"Address": "http://localhost:8200", "Token": "your-vault-token-will-be-set-here", "SecretPath": "secret/yendorcats/app-secrets"}, "AWS": {"Region": "us-west-004", "UseCredentialsFromSecrets": false, "S3": {"BucketName": "yendorcats-images-dev", "UseDirectS3Urls": true, "ServiceUrl": "https://s3.us-west-004.backblazeb2.com", "PublicUrl": "https://f004.backblazeb2.com/file/yendorcats-images-dev/{key}", "UseCdn": false, "CdnDomain": "", "AccessKey": "YOUR_BACKBLAZE_B2_ACCESS_KEY_HERE", "SecretKey": "YOUR_BACKBLAZE_B2_SECRET_KEY_HERE"}}}