using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Models;
using YendorCats.API.Services;
using YendorCats.API.Data;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for handling photo uploads with cat profile linking
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PhotoUploadController : ControllerBase
    {
        private readonly ILogger<PhotoUploadController> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly AppDbContext _context;
        private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
        private readonly string[] _allowedContentTypes = { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
        private const long MaxFileSize = 10 * 1024 * 1024; // 10MB

        public PhotoUploadController(
            ILogger<PhotoUploadController> logger,
            IS3StorageService s3StorageService,
            AppDbContext context)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _context = context;
        }

        /// <summary>
        /// Upload a cat photo with metadata
        /// </summary>
        /// <param name="file">The image file to upload</param>
        /// <param name="catName">Name of the cat</param>
        /// <param name="dateTaken">Date the photo was taken (optional, defaults to today)</param>
        /// <param name="age">Age of the cat in years (optional)</param>
        /// <param name="gender">Gender of the cat (M/F, optional)</param>
        /// <param name="breed">Breed of the cat (optional)</param>
        /// <param name="hairColor">Hair color of the cat (optional)</param>
        /// <param name="eyeColor">Eye color of the cat (optional)</param>
        /// <param name="markings">Special markings or characteristics (optional)</param>
        /// <param name="bloodline">Bloodline information (optional)</param>
        /// <param name="tags">Comma-separated tags (optional)</param>
        /// <param name="notes">Additional notes (optional)</param>
        /// <param name="category">Category for the photo (gallery, studs, queens, kittens)</param>
        /// <returns>Upload result with photo URL and metadata</returns>
        [HttpPost("upload")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UploadPhoto(
            [Required] IFormFile file,
            [Required] string catName,
            DateTime? dateTaken = null,
            float? age = null,
            string? gender = null,
            string? breed = null,
            string? hairColor = null,
            string? eyeColor = null,
            string? markings = null,
            string? bloodline = null,
            string? tags = null,
            string? notes = null,
            string category = "gallery")
        {
            try
            {
                // Validate input
                var validationResult = ValidateUploadRequest(file, catName, category);
                if (validationResult != null)
                {
                    return validationResult;
                }

                _logger.LogInformation("Starting photo upload for cat: {CatName}, Category: {Category}", catName, category);

                // Create metadata object
                var metadata = new CatImageMetadata
                {
                    Name = catName.Trim(),
                    DateTaken = dateTaken ?? DateTime.UtcNow,
                    Age = age,
                    Gender = gender?.Trim().ToUpper() ?? "",
                    Breed = breed?.Trim() ?? "",
                    HairColor = hairColor?.Trim() ?? "",
                    EyeColor = eyeColor?.Trim() ?? "",
                    Markings = markings?.Trim() ?? "",
                    Bloodline = bloodline?.Trim() ?? "",
                    Tags = tags?.Trim() ?? "",
                    Notes = notes?.Trim() ?? "",
                    FileFormat = Path.GetExtension(file.FileName).ToLower(),
                    ContentType = file.ContentType,
                    FileSize = file.Length,
                    UploaderIp = HttpContext.Connection.RemoteIpAddress?.ToString(),
                    UploaderUserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                    UploadSessionId = Guid.NewGuid().ToString()
                };

                // Generate unique filename with cat name and timestamp
                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd-HHmmss");
                var sanitizedCatName = SanitizeFileName(catName);
                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                var uniqueFileName = $"{category}/{sanitizedCatName}-{timestamp}-{Guid.NewGuid().ToString("N")[..8]}{fileExtension}";

                // Upload to S3 with metadata
                using var fileStream = file.OpenReadStream();
                var s3Metadata = metadata.ToS3Metadata();
                var fileUrl = await _s3StorageService.UploadFileWithMetadataAsync(
                    fileStream, uniqueFileName, file.ContentType, s3Metadata);

                // Check if cat profile exists, create if not
                var catProfile = await GetOrCreateCatProfile(catName, metadata);

                // Create gallery image record
                var galleryImage = new CatGalleryImage
                {
                    Id = Guid.NewGuid().ToString(),
                    CatName = catName.Trim(),
                    Age = age ?? 0,
                    DateTaken = dateTaken ?? DateTime.UtcNow,
                    Gender = gender?.Trim().ToUpper() ?? "",
                    Breed = breed?.Trim() ?? "",
                    HairColor = hairColor?.Trim() ?? "",
                    EyeColor = eyeColor?.Trim() ?? "",
                    Markings = markings?.Trim() ?? "",
                    Bloodline = bloodline?.Trim() ?? "",
                    Tags = tags?.Trim() ?? "",
                    Notes = notes?.Trim() ?? "",
                    Category = category,
                    FileName = Path.GetFileName(uniqueFileName),
                    FilePath = uniqueFileName,
                    RelativePath = $"/resources/{category}/{Path.GetFileName(uniqueFileName)}",
                    FileSize = file.Length,
                    ContentType = file.ContentType,
                    DateUploaded = DateTime.UtcNow,
                    S3Url = fileUrl
                };

                var result = new
                {
                    success = true,
                    message = "Photo uploaded successfully",
                    data = new
                    {
                        photoId = galleryImage.Id,
                        catName = galleryImage.CatName,
                        fileName = galleryImage.FileName,
                        fileUrl = fileUrl,
                        category = category,
                        dateTaken = galleryImage.DateTaken,
                        fileSize = file.Length,
                        catProfileId = catProfile?.Id,
                        metadata = galleryImage
                    }
                };

                _logger.LogInformation("Photo uploaded successfully for cat: {CatName}, File: {FileName}, URL: {FileUrl}", 
                    catName, uniqueFileName, fileUrl);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo for cat: {CatName}", catName);
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred while uploading the photo", 
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Get photos for a specific cat (cat profile)
        /// </summary>
        /// <param name="catName">Name of the cat</param>
        /// <param name="orderBy">Order by field (date, name, age)</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>List of photos for the cat</returns>
        [HttpGet("cat/{catName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCatPhotos(
            string catName,
            string orderBy = "date",
            bool descending = true)
        {
            try
            {
                _logger.LogInformation("Getting photos for cat: {CatName}", catName);

                // Get cat profile if exists
                var catProfile = await _context.Cats
                    .Include(c => c.Images)
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == catName.ToLower());

                // Get photos from S3 by scanning for files with this cat's name
                var photos = await GetCatPhotosFromS3(catName, orderBy, descending);

                var result = new
                {
                    success = true,
                    catName = catName,
                    photoCount = photos.Count,
                    catProfile = catProfile != null ? new
                    {
                        id = catProfile.Id,
                        name = catProfile.Name,
                        breed = catProfile.Breed,
                        gender = catProfile.Gender,
                        dateOfBirth = catProfile.DateOfBirth,
                        color = catProfile.Color,
                        description = catProfile.Description,
                        markings = catProfile.Markings,
                        isAvailable = catProfile.IsAvailable
                    } : null,
                    photos = photos,
                    timeline = CreatePhotoTimeline(photos)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photos for cat: {CatName}", catName);
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while retrieving cat photos",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get all cat profiles with photo counts
        /// </summary>
        /// <returns>List of cat profiles with photo counts</returns>
        [HttpGet("profiles")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCatProfiles()
        {
            try
            {
                _logger.LogInformation("Getting all cat profiles");

                // Get all cats from database
                var cats = await _context.Cats
                    .Include(c => c.Images)
                    .Where(c => c.IsActive)
                    .ToListAsync();

                // Get unique cat names from S3 (photos that might not have database entries)
                var s3CatNames = await GetUniqueCatNamesFromS3();

                var profiles = new List<object>();

                // Add database cats
                foreach (var cat in cats)
                {
                    var s3PhotoCount = await GetCatPhotoCountFromS3(cat.Name);
                    profiles.Add(new
                    {
                        id = cat.Id,
                        name = cat.Name,
                        breed = cat.Breed,
                        gender = cat.Gender,
                        dateOfBirth = cat.DateOfBirth,
                        color = cat.Color,
                        description = cat.Description,
                        isAvailable = cat.IsAvailable,
                        databasePhotoCount = cat.Images?.Count ?? 0,
                        s3PhotoCount = s3PhotoCount,
                        totalPhotoCount = s3PhotoCount,
                        hasProfile = true
                    });
                }

                // Add S3-only cats (cats with photos but no database profile)
                foreach (var catName in s3CatNames)
                {
                    if (!cats.Any(c => c.Name.ToLower() == catName.ToLower()))
                    {
                        var photoCount = await GetCatPhotoCountFromS3(catName);
                        profiles.Add(new
                        {
                            id = (int?)null,
                            name = catName,
                            breed = (string?)null,
                            gender = (string?)null,
                            dateOfBirth = (DateTime?)null,
                            color = (string?)null,
                            description = (string?)null,
                            isAvailable = false,
                            databasePhotoCount = 0,
                            s3PhotoCount = photoCount,
                            totalPhotoCount = photoCount,
                            hasProfile = false
                        });
                    }
                }

                var result = new
                {
                    success = true,
                    profileCount = profiles.Count,
                    profiles = profiles.OrderBy(p => ((dynamic)p).name)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cat profiles");
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while retrieving cat profiles",
                    error = ex.Message
                });
            }
        }

        private IActionResult? ValidateUploadRequest(IFormFile file, string catName, string category)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { success = false, message = "No file provided" });
            }

            if (file.Length > MaxFileSize)
            {
                return BadRequest(new { success = false, message = $"File size exceeds maximum allowed size of {MaxFileSize / (1024 * 1024)}MB" });
            }

            if (string.IsNullOrWhiteSpace(catName))
            {
                return BadRequest(new { success = false, message = "Cat name is required" });
            }

            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            if (!_allowedExtensions.Contains(fileExtension))
            {
                return BadRequest(new { success = false, message = $"File type not allowed. Allowed types: {string.Join(", ", _allowedExtensions)}" });
            }

            if (!_allowedContentTypes.Contains(file.ContentType.ToLower()))
            {
                return BadRequest(new { success = false, message = $"Content type not allowed. Allowed types: {string.Join(", ", _allowedContentTypes)}" });
            }

            var validCategories = new[] { "gallery", "studs", "queens", "kittens" };
            if (!validCategories.Contains(category.ToLower()))
            {
                return BadRequest(new { success = false, message = $"Invalid category. Valid categories: {string.Join(", ", validCategories)}" });
            }

            return null;
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return sanitized.Replace(" ", "-").ToLower();
        }

        private async Task<Cat?> GetOrCreateCatProfile(string catName, CatImageMetadata metadata)
        {
            try
            {
                // Check if cat already exists
                var existingCat = await _context.Cats
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == catName.ToLower());

                if (existingCat != null)
                {
                    _logger.LogInformation("Found existing cat profile for: {CatName}", catName);
                    return existingCat;
                }

                // Create new cat profile if we have enough information
                if (!string.IsNullOrEmpty(metadata.Breed) && !string.IsNullOrEmpty(metadata.Gender))
                {
                    var newCat = new Cat
                    {
                        Name = catName.Trim(),
                        Breed = metadata.Breed,
                        Gender = metadata.Gender,
                        Color = metadata.HairColor ?? "Unknown",
                        DateOfBirth = metadata.Age.HasValue ?
                            DateTime.UtcNow.AddYears(-(int)metadata.Age.Value) :
                            DateTime.UtcNow.AddYears(-1), // Default to 1 year old
                        Description = metadata.Notes,
                        Markings = metadata.Markings,
                        Price = 0, // Default price
                        IsAvailable = false, // Default to not available
                        IsActive = true
                    };

                    _context.Cats.Add(newCat);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Created new cat profile for: {CatName}", catName);
                    return newCat;
                }

                _logger.LogInformation("Not enough information to create cat profile for: {CatName}", catName);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting or creating cat profile for: {CatName}", catName);
                return null;
            }
        }

        private async Task<List<object>> GetCatPhotosFromS3(string catName, string orderBy, bool descending)
        {
            try
            {
                // This is a simplified implementation
                // In a real scenario, you would list S3 objects and filter by cat name
                // For now, return empty list - this would be implemented with S3 ListObjects
                var photos = new List<object>();

                _logger.LogInformation("Retrieved {PhotoCount} photos for cat: {CatName}", photos.Count, catName);
                return photos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving photos from S3 for cat: {CatName}", catName);
                return new List<object>();
            }
        }

        private async Task<List<string>> GetUniqueCatNamesFromS3()
        {
            try
            {
                // This would scan S3 bucket for all files and extract unique cat names
                // For now, return empty list - this would be implemented with S3 ListObjects
                var catNames = new List<string>();

                _logger.LogInformation("Retrieved {CatCount} unique cat names from S3", catNames.Count);
                return catNames;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat names from S3");
                return new List<string>();
            }
        }

        private async Task<int> GetCatPhotoCountFromS3(string catName)
        {
            try
            {
                // This would count S3 objects for a specific cat
                // For now, return 0 - this would be implemented with S3 ListObjects
                var count = 0;

                _logger.LogInformation("Found {PhotoCount} photos for cat: {CatName} in S3", count, catName);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error counting photos for cat: {CatName} in S3", catName);
                return 0;
            }
        }

        private object CreatePhotoTimeline(List<object> photos)
        {
            try
            {
                // Group photos by date and create timeline
                var timeline = new
                {
                    totalPhotos = photos.Count,
                    dateRange = photos.Count > 0 ? new
                    {
                        earliest = DateTime.UtcNow, // Would be calculated from actual photos
                        latest = DateTime.UtcNow
                    } : null,
                    monthlyBreakdown = new List<object>() // Would group photos by month
                };

                return timeline;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating photo timeline");
                return new { totalPhotos = 0, dateRange = (object?)null, monthlyBreakdown = new List<object>() };
            }
        }
    }
}
